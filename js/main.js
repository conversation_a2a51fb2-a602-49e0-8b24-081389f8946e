document.addEventListener('DOMContentLoaded', function() {
    // Back to Top Button
    const backToTopButton = document.getElementById('back-to-top');

    // Show/hide back to top button based on scroll position
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
    });

    // Scroll to top when button is clicked
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Mobile Menu Animation
    const menuBtn = document.querySelector('.header-menuImg');
    const navList = document.querySelector('.header-nav');
    const body = document.querySelector('body');

    if (menuBtn) {
        menuBtn.addEventListener('click', function() {
            body.classList.toggle('menu-open');
            navList.classList.toggle('header-nav__active');
            body.classList.toggle('stop-scroll');
        });
    }

    // Close menu when clicking on links
    navList.addEventListener('click', (event) => {
        if (event.target.classList.contains('header-nav__link')) {
            navList.classList.remove('header-nav__active');
            body.classList.remove('stop-scroll');
            body.classList.remove('menu-open');
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (navList.classList.contains('header-nav__active') &&
            !navList.contains(event.target) &&
            !menuBtn.contains(event.target)) {
            navList.classList.remove('header-nav__active');
            body.classList.remove('stop-scroll');
            body.classList.remove('menu-open');
        }
    });

    // Form styling is handled by form.js

    // Add micro-interactions to service items
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Enhanced hero interactions
    const heroButtons = document.querySelectorAll('.hero-btn');
    heroButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Animate trust indicators on scroll
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const trustObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const trustItems = entry.target.querySelectorAll('.hero-trust-item');
                trustItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.animation = `countUp 0.8s ease-out both`;
                    }, index * 200);
                });
            }
        });
    }, observerOptions);

    const heroTrust = document.querySelector('.hero-trust');
    if (heroTrust) {
        trustObserver.observe(heroTrust);
    }

    // Add ripple effect to buttons
    function createRipple(event) {
        const button = event.currentTarget;
        const circle = document.createElement('span');
        const diameter = Math.max(button.clientWidth, button.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
        circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
        circle.classList.add('ripple');

        const ripple = button.getElementsByClassName('ripple')[0];
        if (ripple) {
            ripple.remove();
        }

        button.appendChild(circle);
    }

    heroButtons.forEach(button => {
        button.addEventListener('click', createRipple);
    });
});


// Accordion functionality
const accordionItems = document.querySelectorAll(".accordion-item");

accordionItems.forEach((item) => {
    const header = item.querySelector(".accordion-header");

    header.addEventListener("click", function () {
        // Close all other accordion items
        accordionItems.forEach((otherItem) => {
            if (otherItem !== item) {
                otherItem.classList.remove('active');
            }
        });

        // Toggle current item
        item.classList.toggle('active');
    });
});

// Language selection functionality
const languageSelect = () => {
    const languageInnner = document.querySelector('.header-list__lang');
    const languages = document.querySelectorAll('.header-lang');
    languageInnner.addEventListener('click', (event) => {
        if (event.target.classList.contains('header-lang') && event.target.classList.contains('header-lang__active')) {
            languages.forEach((lang) => {
                lang.classList.remove('header-lang__active');
                lang.classList.add('lang-visible');
            })
        }
    })
    languages.forEach((lang, index) => {
        lang.addEventListener('click', () => {
            lang.classList.add('header-lang__active');
            languages.forEach((otherLang, otherIndex) => {
                if (index === otherIndex) {
                    lang.classList.add('header-lang__active');
                    otherLang.classList.add('lang-visible');
                } else {
                    otherLang.classList.remove('header-lang__active');
                    otherLang.classList.remove('lang-visible');
                }
            });
        });
    });
}

languageSelect()
