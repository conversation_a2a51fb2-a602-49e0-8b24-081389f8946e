/**
 * Hero Services Rotation
 * Handles the dynamic service rotation in the hero visual card
 */

document.addEventListener('DOMContentLoaded', function() {
    // Service data extracted from the website
    const services = [
        {
            title: "Logística Nacional e Internacional",
            description: "Transporte marítimo, aéreo y terrestre con más de 200 proveedores",
            icon: "images/Asset-1.svg"
        },
        {
            title: "Despacho Aduanal",
            description: "Gestión eficiente de trámites aduaneros y comercio exterior",
            icon: "images/Asset-6.svg"
        },
        {
            title: "Freight Forwarder",
            description: "Coordinación completa door to door con seguridad garantizada",
            icon: "images/Asset-3.svg"
        },
        {
            title: "Asesoría y Planificación",
            description: "Estrategias personalizadas para reducir costos y optimizar procesos",
            icon: "images/Asset-7.svg"
        }
    ];

    // DOM elements
    const heroCard = document.getElementById('heroVisualCard');
    const heroIcon = document.getElementById('heroVisualIcon');
    const heroTitle = document.getElementById('heroServiceTitle');
    const heroDescription = document.getElementById('heroServiceDescription');
    const heroContent = document.querySelector('.hero-visual-content');
    const indicators = document.querySelectorAll('.indicator');
    const progressBar = document.getElementById('heroProgressBar');

    // State variables
    let currentServiceIndex = 0;
    let rotationInterval;
    let progressInterval;
    let isPaused = false;
    const rotationDuration = 3000; // 3 seconds per service
    const progressUpdateInterval = 30; // Update progress every 30ms

    // Load SVG icon
    async function loadSVGIcon(iconPath) {
        try {
            const response = await fetch(iconPath);
            const svgText = await response.text();
            return svgText;
        } catch (error) {
            console.warn('Could not load SVG icon:', iconPath);
            // Fallback to a default SVG
            return `<svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                <line x1="12" y1="22.08" x2="12" y2="12"/>
            </svg>`;
        }
    }

    // Update service content
    async function updateService(index, animate = true) {
        const service = services[index];
        
        if (animate) {
            // Fade out current content
            heroContent.classList.add('fade-out');
            
            // Wait for fade out animation
            setTimeout(async () => {
                // Update content
                heroTitle.textContent = service.title;
                heroDescription.textContent = service.description;
                
                // Load and update icon
                const svgContent = await loadSVGIcon(service.icon);
                heroIcon.innerHTML = svgContent;
                
                // Update indicators
                indicators.forEach((indicator, i) => {
                    indicator.classList.toggle('active', i === index);
                });
                
                // Fade in new content
                heroContent.classList.remove('fade-out');
                heroContent.classList.add('fade-in');
                
                // Remove fade-in class after animation
                setTimeout(() => {
                    heroContent.classList.remove('fade-in');
                }, 400);
            }, 200);
        } else {
            // Initial load without animation
            heroTitle.textContent = service.title;
            heroDescription.textContent = service.description;
            
            const svgContent = await loadSVGIcon(service.icon);
            heroIcon.innerHTML = svgContent;
            
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }
    }

    // Start progress bar animation
    function startProgress() {
        let progress = 0;
        progressBar.style.width = '0%';
        
        progressInterval = setInterval(() => {
            if (!isPaused) {
                progress += (progressUpdateInterval / rotationDuration) * 100;
                progressBar.style.width = `${Math.min(progress, 100)}%`;
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                }
            }
        }, progressUpdateInterval);
    }

    // Next service
    function nextService() {
        currentServiceIndex = (currentServiceIndex + 1) % services.length;
        updateService(currentServiceIndex);
        startProgress();
    }

    // Start rotation
    function startRotation() {
        if (rotationInterval) clearInterval(rotationInterval);
        if (progressInterval) clearInterval(progressInterval);
        
        startProgress();
        rotationInterval = setInterval(() => {
            if (!isPaused) {
                nextService();
            }
        }, rotationDuration);
    }

    // Pause rotation
    function pauseRotation() {
        isPaused = true;
        heroCard.classList.add('animate-float');
        heroIcon.classList.add('animate-pulse');
    }

    // Resume rotation
    function resumeRotation() {
        isPaused = false;
        heroCard.classList.remove('animate-float');
        heroIcon.classList.remove('animate-pulse');
    }

    // Initialize
    async function init() {
        // Load initial service
        await updateService(0, false);
        
        // Start rotation
        startRotation();
        
        // Add event listeners
        heroCard.addEventListener('mouseenter', pauseRotation);
        heroCard.addEventListener('mouseleave', resumeRotation);
        
        // Add click listeners to indicators
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                if (currentServiceIndex !== index) {
                    currentServiceIndex = index;
                    updateService(currentServiceIndex);
                    
                    // Restart rotation timer
                    startRotation();
                }
            });
        });
        
        // Add click listener to card for manual advancement
        heroCard.addEventListener('click', (e) => {
            // Don't advance if clicking on indicators
            if (!e.target.classList.contains('indicator')) {
                nextService();
                startRotation();
            }
        });
    }

    // Start the hero services rotation
    init();

    // Handle visibility change (pause when tab is not visible)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            pauseRotation();
        } else {
            resumeRotation();
        }
    });
});
