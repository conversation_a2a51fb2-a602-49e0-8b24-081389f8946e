/* ===== MAIN PAGE SECTIONS ===== */

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: var(--white);
}

/* Background Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        var(--blue-50) 0%,
        var(--white) 35%,
        var(--green-50) 100%);
    opacity: 0.6;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, var(--blue-100) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--green-100) 0%, transparent 50%);
    opacity: 0.3;
}

/* Container */
.hero-container {
    z-index: 2;
    width: 90%;
    margin: 0 auto;
    padding: var(--space-20) var(--space-6);
    display: flex;
    gap: var(--space-16);
    align-items: center;
    justify-content: space-between;
}

/* Content */
.hero-content {
    max-width: 700px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-2) var(--space-4);
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-6);
    animation: fadeInUp 0.8s ease-out;
}

.hero-title {
    font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title-highlight {
    color: var(--primary-blue);
    position: relative;
}

.hero-title-highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
    animation: expandWidth 1s ease-out 1s both;
}

.hero-subtitle {
    font-size: clamp(var(--text-xl), 3vw, var(--text-2xl));
    font-weight: var(--font-medium);
    color: var(--gray-700);
    margin-bottom: var(--space-6);
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-description {
    font-size: var(--text-lg);
    font-weight: var(--font-regular);
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-8);
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

/* Action Buttons */
.hero-actions {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-12);
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.hero-btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.hero-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800));
    transition: var(--transition-base);
    z-index: -1;
}

.hero-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.hero-btn-primary:hover::before {
    left: 0;
}

.hero-btn-secondary {
    background: transparent;
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.hero-btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-2px);
}

.hero-btn-icon {
    transition: transform var(--transition-base);
}

.hero-btn-primary:hover .hero-btn-icon {
    transform: translateX(4px);
}

/* Trust Indicators */
.hero-trust {
    display: flex;
    gap: var(--space-8);
    animation: fadeInUp 0.8s ease-out 1s both;
}

.hero-trust-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.hero-trust-number {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--primary-blue);
    line-height: 1;
}

.hero-trust-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
    font-weight: var(--font-medium);
}

/* Visual Element */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-visual-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    text-align: center;
    max-width: 280px;
    border: 1px solid var(--gray-100);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-base);
}

.hero-visual-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-3xl);
}

.hero-visual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.hero-visual-card:hover::before {
    left: 100%;
}

.hero-visual-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    color: var(--white);
    border-radius: var(--radius-2xl);
    margin: 0 auto var(--space-4);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.hero-visual-icon svg {
    width: 48px;
    height: 48px;
    transition: transform var(--transition-base);
}

.hero-visual-card:hover .hero-visual-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.hero-visual-card:hover .hero-visual-icon svg {
    transform: rotate(5deg);
}

.hero-visual-content {
    transition: all 0.4s ease;
    position: relative;
}

.hero-visual-content.fade-out {
    opacity: 0;
    transform: translateY(10px);
}

.hero-visual-content.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.hero-visual-card h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    transition: color var(--transition-base);
}

.hero-visual-card p {
    font-size: var(--text-base);
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin: 0 0 var(--space-4) 0;
    transition: color var(--transition-base);
}

/* Service Indicators */
.hero-visual-indicators {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-300);
    cursor: pointer;
    transition: all var(--transition-base);
}

.indicator.active {
    background: var(--primary-blue);
    transform: scale(1.2);
}

.indicator:hover {
    background: var(--blue-400);
    transform: scale(1.1);
}

/* Progress Bar */
.hero-visual-progress {
    width: 100%;
    height: 2px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 0.1s linear;
}

/* Animations */
@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes serviceSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes serviceSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-30px);
    }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

.hero-visual-card.animate-float {
    animation: cardFloat 3s ease-in-out infinite;
}

.hero-visual-icon.animate-pulse {
    animation: iconPulse 2s ease-in-out infinite;
}

/* Enhanced Interactivity */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.hero-badge {
    position: relative;
    overflow: hidden;
}

.hero-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

/* Trust indicators animation */
@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.hero-trust-item {
    animation: countUp 0.8s ease-out both;
}

.hero-trust-item:nth-child(1) {
    animation-delay: 1.2s;
}

.hero-trust-item:nth-child(2) {
    animation-delay: 1.4s;
}

.hero-trust-item:nth-child(3) {
    animation-delay: 1.6s;
}

/* Enhanced button interactions */
.hero-btn {
    position: relative;
    overflow: hidden;
}

.hero-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.hero-btn:active::after {
    width: 300px;
    height: 300px;
}

/* Parallax effect for background elements */
@keyframes parallaxFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(5px) rotate(-1deg);
    }
}

.hero-pattern {
    animation: parallaxFloat 20s ease-in-out infinite;
}

/* Typing effect for title highlight */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

.hero-title-highlight::after {
    animation: expandWidth 1s ease-out 1s both, typewriter 0.8s steps(20) 1.2s both;
}

/* Ripple effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== SERVICES SECTION ===== */
.service {
    width: 90%;
    margin: 0 auto;
    margin-bottom: var(--space-24);
    padding: var(--space-16) 0;
}

.service h2 {
    position: relative;
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--gray-900);
    text-align: center;
    margin: 0 auto var(--space-16);
    padding: var(--space-4) 0;
    max-width: 621px;
}

.service h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-12);
    max-width: 1342px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.service-item {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-base);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.service-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-item:hover::before {
    transform: scaleX(1);
}

.service-item__pic {
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: center;
}

.service-item__pic img {
    max-width: 120px;
    width: 100%;
    height: auto;
    transition: var(--transition-base);
}

.service-item:hover .service-item__pic img {
    transform: scale(1.1);
}

.service-item__title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--primary-blue);
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.service-item__text {
    font-size: var(--text-base);
    font-weight: var(--font-regular);
    color: var(--gray-700);
    line-height: var(--leading-relaxed);
}

/* ===== MIDDLE CTA SECTION ===== */
.middle {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--blue-700) 100%);
    min-height: 355px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    margin: var(--space-24) 0;
    overflow: hidden;
}

.middle::after {
    content: "";
    position: absolute;
    clip-path: polygon(82% 0, 100% 0, 100% 100%, 49% 100%);
    background: linear-gradient(135deg, rgba(123, 139, 167, 0.1), rgba(123, 139, 167, 0.05));
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.middle div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
    padding: var(--space-8);
}

.middle div p {
    text-align: center;
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-xl), 3vw, var(--text-3xl));
    color: var(--white);
    margin-bottom: var(--space-6);
    max-width: 800px;
    line-height: var(--leading-relaxed);
}

.middle div a {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    padding: var(--space-4) var(--space-8);
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    color: var(--white);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-base);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.middle div a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.middle div a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.middle div a:hover::before {
    left: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablets */
@media (max-width: 1024px) {
    .hero {
        min-height: 90vh;
    }

    .hero-container {
        flex-direction: column;
        align-items: start;
        padding: var(--space-16) var(--space-6);
    }

    .hero-title {
        font-size: clamp(var(--text-3xl), 6vw, var(--text-5xl));
    }

    .hero-subtitle {
        font-size: clamp(var(--text-lg), 4vw, var(--text-xl));
    }

    .hero-trust {
        gap: var(--space-6);
    }

    .service-items {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-8);
    }
}

/* Mobile Landscape and Small Tablets */
@media (max-width: 768px) {
    .hero {
        min-height: 80vh;
    }

    .hero-container {
        padding: var(--space-12) var(--space-4);
        gap: var(--space-8);
    }

    .hero-actions {
        gap: var(--space-3);
    }

    .hero-btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-trust {
        gap: var(--space-4);
    }

    .hero-trust-item {
        align-items: center;
        text-align: center;
    }

    .hero-visual-card {
        max-width: 240px;
        padding: var(--space-6);
    }

    .hero-visual-icon {
        width: 70px;
        height: 70px;
    }

    .hero-visual-icon svg {
        width: 40px;
        height: 40px;
    }

    .service {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-16);
    }

    .service-items {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        padding: 0 var(--space-4);
    }

    .service-item {
        padding: var(--space-6);
    }

    .middle {
        min-height: 280px;
        margin: var(--space-16) 0;
    }

    .middle div {
        padding: var(--space-6);
    }
}

/* Mobile Portrait */
@media (max-width: 480px) {
    .hero {
        min-height: 70vh;
    }

    .hero-container {
        padding: var(--space-10) var(--space-4);
    }

    .hero-title {
        font-size: clamp(var(--text-2xl), 8vw, var(--text-4xl));
        line-height: var(--leading-tight);
    }

    .hero-subtitle {
        font-size: clamp(var(--text-base), 5vw, var(--text-lg));
    }

    .hero-description {
        font-size: var(--text-base);
    }

    .hero-badge {
        font-size: var(--text-xs);
        padding: var(--space-1) var(--space-3);
    }

    .hero-visual-card {
        max-width: 200px;
        padding: var(--space-4);
    }

    .hero-visual-icon {
        width: 60px;
        height: 60px;
    }

    .hero-visual-icon svg {
        width: 32px;
        height: 32px;
    }

    .hero-visual-card h3 {
        font-size: var(--text-lg);
    }

    .hero-visual-card p {
        font-size: var(--text-sm);
    }

    .indicator {
        width: 6px;
        height: 6px;
    }

    .service-item__pic img {
        max-width: 80px;
    }

    .middle::after {
        clip-path: polygon(90% 0, 100% 0, 100% 100%, 70% 100%);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .hero-badge,
    .hero-title,
    .hero-subtitle,
    .hero-description,
    .hero-actions,
    .hero-trust,
    .hero-visual,
    .hero-title-highlight::after,
    .hero-visual-card.animate-float,
    .hero-visual-icon.animate-pulse,
    .hero-badge::before,
    .hero-trust-item,
    .hero-pattern,
    .ripple {
        animation: none;
    }

    .hero-btn,
    .hero-btn-icon,
    .hero-visual-content,
    .hero-visual-card,
    .hero-visual-icon,
    .service-item,
    .service-item__pic img,
    .middle div a {
        transition: none;
    }

    .hero-btn:hover,
    .hero-visual-card:hover {
        transform: none;
    }

    .hero-btn-primary:hover .hero-btn-icon {
        transform: none;
    }

    .hero-visual-card:hover .hero-visual-icon {
        transform: none;
    }

    .hero-visual-card:hover .hero-visual-icon svg {
        transform: none;
    }
}

/* High Contrast */
@media (prefers-contrast: high) {
    .hero-btn-primary {
        border: 2px solid var(--white);
    }

    .hero-btn-secondary {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .hero-visual-card {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .service-item {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .middle div a {
        border: 2px solid var(--white);
    }
}

/* Focus States for Better Accessibility */
.hero-btn:focus-visible {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

.hero-btn-secondary:focus-visible {
    outline-color: var(--gray-700);
}

/* ===== WHY CHOOSE US SECTION ===== */
.us {
    width: 90%;
    margin: 0 auto;
    margin-bottom: var(--space-16);
    padding: var(--space-16) 0;
}

.us-inner {
    margin-bottom: var(--space-12);
    text-align: center;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 var(--space-5);
}

.us h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    text-align: center;
    margin-bottom: var(--space-8);
    position: relative;
    padding-bottom: var(--space-4);
}

.us h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.us-inner p {
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-600);
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.us-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.us-item {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-base);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.us-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green), var(--green-600));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.us-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.us-item:hover::before {
    transform: scaleX(1);
}

.us-item div {
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: center;
}

.us-item img {
    width: 80px;
    height: 80px;
    transition: var(--transition-base);
}

.us-item:hover img {
    transform: scale(1.1);
}

.us-item h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.us-item p {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
}

/* ===== ABOUT SECTION ===== */
.about {
    width: 90%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    margin-bottom: var(--space-32);
    gap: var(--space-16);
    padding: var(--space-16) 0;
}

.about-pics {
    display: flex;
    gap: var(--space-4);
    flex: 1;
    max-width: 600px;
}

.about-pics > div:first-child {
    flex: 2;
}

.about-pics > div:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.about-pics img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-base);
}

.about-pics img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.about-info {
    flex: 1;
    max-width: 795px;
    padding: 0 var(--space-5);
}

.about-info h3 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    position: relative;
    padding-bottom: var(--space-4);
}

.about-info h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.about-info > p {
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.about-info__items {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.about-info__item {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
}

.about-info__item-pic {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--blue-100), var(--blue-200));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-info__item-pic img {
    width: 22px;
    height: 22px;
}

.about-info__item-text span {
    color: var(--blue-800);
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    display: block;
    margin-bottom: var(--space-2);
}

.about-info__item-text p {
    color: var(--gray-600);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-regular);
    margin: 0;
}

/* ===== CONSULTATION SECTION ===== */
.consultation {
    margin-bottom: var(--space-32);
    background-image: url("../images/about-8.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.consultation::after {
    content: "";
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 88, 168, 0.4));
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.consultation-items {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--space-16) var(--space-5);
    max-width: 1000px;
}

.consultation-items h4 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 5vw, var(--text-6xl));
    color: var(--white);
    text-align: center;
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.consultation-items p {
    font-size: clamp(var(--text-xl), 3vw, var(--text-4xl));
    font-weight: var(--font-regular);
    margin-bottom: var(--space-8);
    color: var(--white);
    text-align: center;
    line-height: var(--leading-relaxed);
}

.consultation-items a {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    padding: var(--space-4) var(--space-12);
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    color: var(--white);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-base);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.consultation-items a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.consultation-items a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
}

.consultation-items a:hover::before {
    left: 0;
}

/* ===== QUESTIONS/FAQ SECTION ===== */
.questions {
    margin-bottom: var(--space-16);
    padding: var(--space-16) 0;
}

.questions h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    text-align: center;
    margin-bottom: var(--space-16);
    position: relative;
    padding-bottom: var(--space-4);
}

.questions h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.questions-accordion {
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.accordion-item {
    background: var(--white);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-4);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-base);
}

.accordion-item:hover {
    box-shadow: var(--shadow-md);
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-8);
    cursor: pointer;
    transition: var(--transition-base);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
}

.accordion-header:hover {
    background: linear-gradient(135deg, var(--blue-50), var(--gray-50));
}

.accordion-header h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin: 0;
    line-height: var(--leading-tight);
    flex: 1;
    padding-right: var(--space-4);
}

.accordion-header img {
    width: 24px;
    height: 24px;
    transition: var(--transition-base);
    filter: brightness(0) saturate(100%) invert(25%) sepia(15%) saturate(1000%) hue-rotate(200deg);
}

.accordion-item.active .accordion-header img {
    transform: rotate(180deg);
    filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}

.accordion-content {
    padding: 0 var(--space-8);
    color: var(--gray-700);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-item.active .accordion-content {
    max-height: 200px;
    padding: var(--space-4) var(--space-8) var(--space-6);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonios-section {
    padding: var(--space-16) 0;
    text-align: center;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    margin: var(--space-16) 0;
}

.testimonios-section h2 {
    font-weight: var(--font-bold);
    color: var(--blue-800);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    margin-bottom: var(--space-12);
    position: relative;
    padding-bottom: var(--space-4);
}

.testimonios-section h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.testimonios-container {
    width: 90%;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl);
    background: var(--white);
    box-shadow: var(--shadow-lg);
    padding: var(--space-8);
}

.testimonio {
    display: none;
    text-align: center;
    animation: fadeInUp 0.5s ease;
}

.testimonio.active {
    display: block;
}

.testimonio blockquote {
    margin: 0 0 var(--space-6) 0;
    padding: 0;
}

.testimonio blockquote p {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
    font-style: italic;
    position: relative;
    padding: 0 var(--space-4);
}

.testimonio blockquote p::before {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    position: absolute;
    left: -10px;
    top: -10px;
    font-family: serif;
}

.testimonio blockquote p::after {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    position: absolute;
    right: -10px;
    bottom: -20px;
    font-family: serif;
}

.testimonio > p {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--blue-800);
    margin-bottom: var(--space-2);
}

.testimonio span {
    font-size: var(--text-base);
    color: var(--gray-600);
    font-style: italic;
}

.testimonios-controles {
    margin-top: var(--space-8);
    display: flex;
    justify-content: center;
    gap: var(--space-5);
}

.testimonios-prev,
.testimonios-next {
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    border: none;
    color: var(--white);
    font-size: var(--text-xl);
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.testimonios-prev:hover,
.testimonios-next:hover {
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== CONTACT SECTION ===== */
.contact {
    height: 90vh;
    margin: 80px 0;
    padding: 80px 0;
    /* background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); */
}

.contact-container {
    height: 100%;
    width: 90%;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.contact-form-column {
    width: 65%;
}

.contact-info-column {
    width: 30%;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    position: relative;
    padding-bottom: var(--space-4);
}

.contact h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.contact-text {
    margin-bottom: var(--space-8);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-regular);
    color: var(--gray-600);
    max-width: 600px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
}

.contact-label {
    display: block;
    width: 100%;
}

.contact-input,
.contact-label__textarea,
.contact-label__select select {
    width: 100%;
    font-weight: var(--font-regular);
    font-size: var(--text-base);
    padding: var(--space-4) var(--space-6);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    transition: var(--transition-base);
    font-family: var(--font-primary);
}

.contact-input:focus,
.contact-label__textarea:focus,
.contact-label__select select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 88, 168, 0.1);
}

.contact-input::placeholder,
.contact-label__textarea::placeholder {
    color: var(--gray-400);
}

.contact-label__select select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-4) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--space-12);
}

.contact-label__textarea {
    min-height: 120px;
    resize: vertical;
}

.contact-btn {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    width: 100%;
    padding: var(--space-4) var(--space-6);
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-base);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800));
    transition: var(--transition-base);
    z-index: -1;
}

.contact-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-btn:hover:not(:disabled)::before {
    left: 0;
}

.contact-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Form Validation States */
.contact-input.error-input,
.contact-label__textarea.error-input {
    border-color: var(--error);
    background-color: var(--error-light);
}

.contact-input.valid-input,
.contact-label__textarea.valid-input {
    border-color: var(--success);
    background-color: var(--success-light);
}

.contact-mobile__img {
    display: none;
}
.contact h2 {
    font-weight: 700;
    font-size: clamp(32px, 4vw, 48px);
    color: #000E49;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.contact h2::after {
    content: "";
    display: block;
    border: 2px solid #02E600;
    width: 60px;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 2px;
}

.contact-text {
    margin-bottom: 30px;
    font-size: 18px;
    line-height: 28px;
    font-weight: 400;
    color: #7B8BA7;
}

/* Form Layout */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.contact-label {
    display: block;
    width: 100%;
}

.contact-input, .contact-textarea {
    width: 100%;
    font-weight: 400;
    font-size: 16px;
    padding: 16px 20px;
    color: #333;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
    font-family: inherit;
}

.contact-input:focus, .contact-textarea:focus {
    outline: none;
    border-color: #000E49;
    box-shadow: 0 0 0 3px rgba(0, 14, 73, 0.1);
    transform: translateY(-1px);
}

.contact-input:focus-visible, .contact-textarea:focus-visible {
    outline: 2px solid #000E49;
    outline-offset: 2px;
}

.contact-input::placeholder, .contact-textarea::placeholder {
    color: #94a3b8;
}

.contact-label__select select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 50px;
}

.contact-textarea {
    min-height: 120px;
    resize: vertical;
}

/* Team Photo */
.contact-team-photo {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 14, 73, 0.1);
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.contact-team-photo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 14, 73, 0.05) 0%, rgba(2, 230, 0, 0.05) 100%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-team-photo:hover::before {
    opacity: 1;
}

.contact-team-photo img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
    filter: brightness(1.05) contrast(1.02) saturate(1.1);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.contact-team-photo:hover img {
    transform: scale(1.02);
    filter: brightness(1.1) contrast(1.05) saturate(1.15);
}

/* Map Section with Integrated Contact Info */
.contact-map-section {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 14, 73, 0.1);
}

.contact-map {
    position: relative;
}

.contact-map iframe {
    width: 100%;
    height: 300px;
    border: none;
    display: block;
}

/* Contact Details Overlay */
.contact-details-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 14, 73, 0.95) 0%, rgba(0, 14, 73, 0.8) 70%, transparent 100%);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    backdrop-filter: blur(2px);
    transition: all 0.3s ease;
}

.contact-map-section:hover .contact-details-overlay {
    background: linear-gradient(to top, rgba(0, 14, 73, 0.98) 0%, rgba(0, 14, 73, 0.85) 70%, transparent 100%);
}

.contact-detail-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
}

.contact-detail-compact svg {
    flex-shrink: 0;
    opacity: 0.9;
}

.contact-detail-compact a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.contact-detail-compact a:hover {
    opacity: 0.8;
}

.contact-detail-compact span {
    color: rgba(255, 255, 255, 0.9);
}

/* Submit Button */
.contact-btn {
    font-size: 18px;
    font-weight: 600;
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #000E49, #1e40af);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 14, 73, 0.3);
}

.contact-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Form Validation Styles */
.error-message {
    color: #dc2626;
    font-size: 13px;
    margin-top: 6px;
    font-weight: 500;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.contact-input.error-input,
.contact-textarea.error-input {
    border-color: #dc2626;
    background-color: rgba(220, 38, 38, 0.05);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.contact-input.valid-input,
.contact-textarea.valid-input {
    border-color: #16a34a;
    background-color: rgba(22, 163, 74, 0.05);
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

/* Button Loader Override */
.form-submit-container {
    position: relative;
    margin-top: 10px;
}

.btn-loader {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Legacy styles cleanup - keeping for compatibility */
.contact-mobile__img {
    display: none;
}
/* ===== ADDITIONAL RESPONSIVE DESIGN ===== */

/* Large Tablets */
@media (max-width: 1240px) {
    .about {
        flex-direction: column-reverse;
        gap: var(--space-12);
        text-align: center;
    }

    .about-pics {
        max-width: 100%;
        justify-content: center;
    }

    .about-info {
        max-width: 100%;
        padding: 0 var(--space-4);
    }

    .about-info h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .us-items {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-6);
    }
}

@media (max-width: 1000px) {
    .contact {
        height: auto;
    }
    .contact-container {
        height: auto;
        flex-direction: column;
    }

    .contact-container .contact-form-column {
        width: 100%;
    }

    .contact-container .contact-info-column {
        width: 100%;
    }
    .contact-container .contact-info-column .contact-team-photo {
        display: none;
    }
}

@media (max-width: 650px) {
    .contact-container .form-row {
        display: flex;
        flex-direction: column;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .us {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-12);
    }

    .us-items {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        padding: 0 var(--space-4);
    }

    .us-item {
        padding: var(--space-6);
    }

    .about {
        margin-bottom: var(--space-20);
        padding: var(--space-12) 0;
    }

    .about-pics {
        flex-direction: column;
        gap: var(--space-4);
    }

    .about-pics > div:last-child {
        flex-direction: row;
        gap: var(--space-3);
    }

    .consultation {
        min-height: 400px;
        margin-bottom: var(--space-20);
    }

    .consultation-items {
        padding: var(--space-12) var(--space-4);
    }

    .questions {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-12);
    }

    .questions-accordion {
        padding: 0 var(--space-4);
    }

    .accordion-header {
        padding: var(--space-4) var(--space-5);
    }

    .accordion-header h3 {
        font-size: var(--text-base);
    }

    .accordion-content {
        padding: 0 var(--space-5);
    }

    .accordion-item.active .accordion-content {
        padding: var(--space-3) var(--space-5) var(--space-4);
    }

    .testimonios-section {
        padding: var(--space-12) 0;
        margin: var(--space-12) 0;
    }

    .testimonios-container {
        width: 95%;
        padding: var(--space-6);
    }

    .testimonio blockquote p {
        font-size: var(--text-base);
        padding: 0 var(--space-2);
    }

    .testimonios-controles {
        gap: var(--space-4);
    }

    .testimonios-prev,
    .testimonios-next {
        width: 45px;
        height: 45px;
        font-size: var(--text-lg);
    }

    .contact {
        padding: var(--space-20) 0;
        margin-bottom: var(--space-16);
    }

    .contact-inner {
        padding: 0 var(--space-4);
    }

    .contact h2::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .contact-text {
        text-align: center;
        margin: 0 auto var(--space-8);
    }
}

/* Mobile */
@media (max-width: 480px) {
    .us-item {
        padding: var(--space-4);
    }

    .us-item img {
        width: 60px;
        height: 60px;
    }

    .about-pics > div:last-child {
        flex-direction: column;
    }

    .about-info__item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .about-info__item-pic {
        align-self: center;
    }

    .consultation {
        min-height: 350px;
    }

    .consultation-items {
        padding: var(--space-8) var(--space-3);
    }

    .consultation-items a {
        padding: var(--space-3) var(--space-8);
        font-size: var(--text-lg);
    }

    .accordion-header {
        padding: var(--space-3) var(--space-4);
    }

    .accordion-header h3 {
        font-size: var(--text-sm);
    }

    .accordion-header img {
        width: 20px;
        height: 20px;
    }

    .testimonios-container {
        padding: var(--space-4);
    }

    .testimonio blockquote p::before,
    .testimonio blockquote p::after {
        font-size: var(--text-2xl);
    }

    .testimonios-prev,
    .testimonios-next {
        width: 40px;
        height: 40px;
        font-size: var(--text-base);
    }

    .contact-input,
    .contact-label__textarea,
    .contact-label__select select {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-base);
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    .us-item,
    .about-pics img,
    .consultation-items a,
    .accordion-item,
    .testimonios-prev,
    .testimonios-next,
    .contact-btn {
        transition: none;
    }

    .testimonio {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .us-item,
    .accordion-item,
    .testimonios-container {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .consultation-items a,
    .testimonios-prev,
    .testimonios-next,
    .contact-btn {
        border: 2px solid var(--white);
    }

    .contact-input,
    .contact-label__textarea,
    .contact-label__select select {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .contact-input:focus,
    .contact-label__textarea:focus,
    .contact-label__select select:focus {
        border-color: var(--primary-blue);
        box-shadow: none;
    }
}

/* Focus Styles for Accessibility */
.us-item:focus,
.accordion-header:focus,
.testimonios-prev:focus,
.testimonios-next:focus,
.consultation-items a:focus,
.contact-input:focus,
.contact-label__textarea:focus,
.contact-label__select select:focus,
.contact-btn:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}
