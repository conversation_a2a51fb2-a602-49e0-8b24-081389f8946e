/* ===== STI LOGÍSTICA - MODULAR CSS ARCHITECTURE ===== */
/* Modern, scalable CSS architecture with design system approach */

/* ===== CSS MODULE IMPORTS ===== */
@import 'variables.css';    /* Design system variables and tokens */
@import 'navbar.css';       /* Header and navigation styles */
@import 'footer.css';       /* Footer styles with social media */
@import 'components.css';   /* Reusable UI components */
@import 'sections.css';     /* Main page sections */

/* ===== BASE STYLES ===== */

/* Global Reset and Base */
*,
*::after,
*::before {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-primary);
    background-color: var(--white);
    color: var(--gray-900);
    line-height: var(--leading-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Typography Base */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    color: var(--gray-900);
}

h1 {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
}

h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
}

h3 {
    font-size: var(--text-3xl);
    font-weight: var(--font-semibold);
}

h4 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}

h5 {
    font-size: var(--text-xl);
    font-weight: var(--font-medium);
}

h6 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}

p {
    margin: 0 0 var(--space-4) 0;
    line-height: var(--leading-relaxed);
}

/* Links */
a {
    text-decoration: none;
    color: var(--primary-blue);
    transition: var(--transition-base);
}

a:hover {
    color: var(--blue-700);
}

a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Lists */
ul, ol {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Container System */
.container {
    margin: 0 auto;
    max-width: var(--container-2xl);
    width: 90%;
}

.container-sm {
    max-width: var(--container-sm);
}

.container-md {
    max-width: var(--container-md);
}

.container-lg {
    max-width: var(--container-lg);
}

.container-xl {
    max-width: var(--container-xl);
}

/* Section Spacing */
.section {
    padding: var(--space-16) 0;
}

.section-sm {
    padding: var(--space-12) 0;
}

.section-lg {
    padding: var(--space-24) 0;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Flexbox Utilities */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

/* Grid Utilities */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
    gap: var(--space-4);
}

.gap-6 {
    gap: var(--space-6);
}

.gap-8 {
    gap: var(--space-8);
}

/* Spacing Utilities */
.mb-4 {
    margin-bottom: var(--space-4);
}

.mb-6 {
    margin-bottom: var(--space-6);
}

.mb-8 {
    margin-bottom: var(--space-8);
}

.mt-4 {
    margin-top: var(--space-4);
}

.mt-6 {
    margin-top: var(--space-6);
}

.mt-8 {
    margin-top: var(--space-8);
}

/* Legacy Support Classes */
.title {
    position: relative;
    font-weight: var(--font-bold);
    font-size: var(--text-4xl);
    color: var(--gray-900);
    text-align: center;
    margin: 0 auto var(--space-16);
    padding: var(--space-4) 0;
    max-width: 621px;
}

.title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

/* Form Submit Container */
.form-submit-container {
    display: flex;
    justify-content: center;
    margin-top: var(--space-6);
}

/* Button Loading States */
.btn-text {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-loader {
    display: none;
}

.btn-loading .btn-text {
    display: none;
}

.btn-loading .btn-loader {
    display: inline-flex;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

.spinner .path {
    stroke: currentColor;
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .spinner {
        animation: none;
    }

    .spinner .path {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    body {
        background: var(--white);
        color: var(--black);
    }

    a {
        color: var(--black);
        text-decoration: underline;
    }

    .title::after {
        background: var(--black);
    }
}

/* Focus Management */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-blue);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-base);
    z-index: var(--z-toast);
}

.skip-link:focus {
    top: 6px;
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .back-to-top,
    .testimonios-controles,
    .contact-form {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }

    .container {
        max-width: none;
        padding: 0;
    }
}
